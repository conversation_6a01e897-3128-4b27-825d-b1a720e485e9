package com.pokecobble.town;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Represents a player in a town.
 */
public class TownPlayer {
    private final UUID uuid;
    private String name;
    private TownPlayerRank rank;
    private boolean online;

    // Store permissions by category
    private final Map<String, Map<String, Boolean>> permissions = new HashMap<>();

    // Version tracking for data synchronization
    private int dataVersion = 0;

    /**
     * Creates a new town player.
     *
     * @param uuid The player's UUID
     * @param name The player's name
     * @param rank The player's rank
     * @param online Whether the player is online
     */
    public TownPlayer(UUID uuid, String name, TownPlayerRank rank, boolean online) {
        this.uuid = uuid;
        this.name = name;
        this.rank = rank;
        this.online = online;

        // Try to load permissions from file-based storage first
        boolean permissionsLoaded = loadPermissionsFromFileStorage();
        if (!permissionsLoaded) {
            // If no permissions found in file storage, initialize default permissions
            com.pokecobble.Pokecobbleclaim.LOGGER.debug("No existing permissions found for " + this.name + " - initializing defaults");
            initializeDefaultPermissions();
        } else {
            com.pokecobble.Pokecobbleclaim.LOGGER.debug("Successfully loaded existing permissions for " + this.name);
        }
    }

    /**
     * Creates a new town player.
     *
     * @param uuid The player's UUID
     * @param name The player's name
     * @param rank The player's rank
     */
    public TownPlayer(UUID uuid, String name, TownPlayerRank rank) {
        this(uuid, name, rank, false);
    }

    /**
     * Creates a new town player without initializing default permissions.
     * This constructor is used when loading player data from disk to preserve custom permissions.
     *
     * @param uuid The player's UUID
     * @param name The player's name
     * @param rank The player's rank
     * @param online Whether the player is online
     * @param preservePermissions If true, doesn't initialize default permissions (for loading from disk)
     */
    private TownPlayer(UUID uuid, String name, TownPlayerRank rank, boolean online, boolean preservePermissions) {
        this.uuid = uuid;
        this.name = name;
        this.rank = rank;
        this.online = online;

        // Only initialize default permissions if not preserving existing ones
        if (!preservePermissions) {
            initializeDefaultPermissions();
        }
    }

    /**
     * Creates a TownPlayer from saved data without overwriting custom permissions.
     * This method should be used when loading player data from disk.
     *
     * @param uuid The player's UUID
     * @param name The player's name
     * @param rank The player's rank
     * @param savedPermissions The saved permissions map
     * @return A TownPlayer with the saved permissions preserved
     */
    public static TownPlayer fromSavedData(UUID uuid, String name, TownPlayerRank rank,
                                          Map<String, Map<String, Boolean>> savedPermissions) {
        // Create player without initializing default permissions
        TownPlayer player = new TownPlayer(uuid, name, rank, false, true);

        // Set the saved permissions
        if (savedPermissions != null) {
            com.pokecobble.Pokecobbleclaim.LOGGER.debug("Setting saved permissions for player " + name + " - categories: " + savedPermissions.keySet());
            for (Map.Entry<String, Map<String, Boolean>> entry : savedPermissions.entrySet()) {
                String category = entry.getKey();
                Map<String, Boolean> categoryPerms = entry.getValue();
                player.setCategoryPermissions(category, categoryPerms);

                // Log claim tool permissions specifically
                if ("Claim Tool".equals(category) && categoryPerms.containsKey("Can access claim tool")) {
                    com.pokecobble.Pokecobbleclaim.LOGGER.info("Restored claim tool access for " + name + ": " + categoryPerms.get("Can access claim tool"));
                }
            }
        } else {
            // If no saved permissions, initialize defaults
            com.pokecobble.Pokecobbleclaim.LOGGER.debug("No saved permissions for player " + name + ", initializing defaults");
            player.initializeDefaultPermissions();
        }

        return player;
    }

    /**
     * Initializes default permissions based on the player's rank.
     */
    private void initializeDefaultPermissions() {
        // Player Management permissions
        Map<String, Boolean> playerManageToggles = new HashMap<>();
        playerManageToggles.put("Can kick players", rank.hasAdminPermissions() || rank.ordinal() <= TownPlayerRank.ADMIN.ordinal());
        playerManageToggles.put("Can change player ranks", rank.hasAdminPermissions() || rank.ordinal() <= TownPlayerRank.ADMIN.ordinal());
        playerManageToggles.put("Can manage player permissions", rank.hasAdminPermissions() || rank.ordinal() <= TownPlayerRank.ADMIN.ordinal());
        playerManageToggles.put("Can invite players", rank.hasAdminPermissions() || rank.ordinal() <= TownPlayerRank.MEMBER.ordinal());
        permissions.put("Player Management", playerManageToggles);

        // Job permissions
        Map<String, Boolean> jobToggles = new HashMap<>();
        jobToggles.put("Can request building approval", rank.hasAdminPermissions() || rank.ordinal() <= TownPlayerRank.MEMBER.ordinal());
        jobToggles.put("Can view job applications", rank.hasAdminPermissions() || rank.ordinal() <= TownPlayerRank.MEMBER.ordinal());
        jobToggles.put("Can apply for jobs", rank.hasAdminPermissions() || rank.ordinal() <= TownPlayerRank.MEMBER.ordinal());
        jobToggles.put("Can manage town jobs", rank.hasAdminPermissions() || rank.ordinal() <= TownPlayerRank.ADMIN.ordinal());
        permissions.put("Job", jobToggles);

        // Claim Tool permissions
        Map<String, Boolean> claimToggles = new HashMap<>();
        claimToggles.put("Can view claims", true); // Everyone can view claims

        // Only admin ranks get access to the claim tool by default
        boolean hasAdminAccess = rank.hasAdminPermissions();
        claimToggles.put("Can access claim tool", hasAdminAccess); // Admin ranks get access
        claimToggles.put("Can delete claims", hasAdminAccess); // Admin ranks can delete
        permissions.put("Claim Tool", claimToggles);

        // Town Bank permissions
        Map<String, Boolean> bankToggles = new HashMap<>();
        bankToggles.put("Can view balance", rank.hasAdminPermissions() || rank.ordinal() <= TownPlayerRank.MEMBER.ordinal());
        bankToggles.put("Can deposit money", rank.hasAdminPermissions() || rank.ordinal() <= TownPlayerRank.MEMBER.ordinal());
        bankToggles.put("Can withdraw money", rank.hasAdminPermissions() || rank.ordinal() <= TownPlayerRank.ADMIN.ordinal());
        bankToggles.put("Can view transactions", rank.hasAdminPermissions() || rank.ordinal() <= TownPlayerRank.ADMIN.ordinal());
        permissions.put("Town Bank", bankToggles);

        // Town Level permissions
        Map<String, Boolean> levelToggles = new HashMap<>();
        levelToggles.put("Can view level", rank.hasAdminPermissions() || rank.ordinal() <= TownPlayerRank.MEMBER.ordinal());
        levelToggles.put("Can contribute resources", rank.hasAdminPermissions() || rank.ordinal() <= TownPlayerRank.MEMBER.ordinal());
        levelToggles.put("Can view benefits", rank.hasAdminPermissions() || rank.ordinal() <= TownPlayerRank.MEMBER.ordinal());
        permissions.put("Town Level", levelToggles);

        // Town Settings permissions
        Map<String, Boolean> settingsToggles = new HashMap<>();
        settingsToggles.put("Can modify settings", rank.hasAdminPermissions() || rank.ordinal() <= TownPlayerRank.ADMIN.ordinal());
        settingsToggles.put("Can change town name", rank == TownPlayerRank.OWNER || rank == TownPlayerRank.ADMIN_VIEWER);
        settingsToggles.put("Can change town description", rank.hasAdminPermissions() || rank.ordinal() <= TownPlayerRank.ADMIN.ordinal());
        settingsToggles.put("Can change town spawn", rank.hasAdminPermissions() || rank.ordinal() <= TownPlayerRank.ADMIN.ordinal());
        permissions.put("Town Settings", settingsToggles);
    }

    /**
     * Gets the player's UUID.
     *
     * @return The player's UUID
     */
    public UUID getUuid() {
        return uuid;
    }

    /**
     * Gets the player's name.
     *
     * @return The player's name
     */
    public String getName() {
        return name;
    }

    /**
     * Sets the player's name.
     *
     * @param name The new name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * Gets the player's rank.
     *
     * @return The player's rank
     */
    public TownPlayerRank getRank() {
        return rank;
    }

    /**
     * Sets the player's rank.
     *
     * @param rank The new rank
     */
    public void setRank(TownPlayerRank rank) {
        this.rank = rank;

        // Increment data version since data has changed
        incrementDataVersion();

        // Note: Rank change synchronization is now handled by the Town class
        // using the simplified rank change system. This avoids complex permission tracking.
    }

    /**
     * Sets the player's rank internally without triggering synchronization.
     * This is used by the Town class to sync TownPlayer objects with authoritative rank data.
     *
     * @param rank The new rank
     */
    public void setRankInternal(TownPlayerRank rank) {
        this.rank = rank;
        // Increment data version since data has changed
        incrementDataVersion();
        // No synchronization triggers - this is for internal consistency only
    }

    /**
     * Checks if the player is online.
     *
     * @return True if the player is online, false otherwise
     */
    public boolean isOnline() {
        return online;
    }

    /**
     * Sets whether the player is online.
     *
     * @param online True if the player is online, false otherwise
     */
    public void setOnline(boolean online) {
        if (this.online != online) {
            this.online = online;

            // Increment data version since data has changed
            incrementDataVersion();
        }
    }

    /**
     * Gets all permissions for a specific category.
     *
     * @param category The category name
     * @return A map of permission names to boolean values
     */
    public Map<String, Boolean> getCategoryPermissions(String category) {
        return permissions.getOrDefault(category, new HashMap<>());
    }

    /**
     * Sets all permissions for a specific category.
     *
     * @param category The category name
     * @param categoryPermissions The new permissions
     */
    public void setCategoryPermissions(String category, Map<String, Boolean> categoryPermissions) {
        permissions.put(category, new HashMap<>(categoryPermissions));

        // Debug logging for claim tool permissions
        if ("Claim Tool".equals(category) && categoryPermissions.containsKey("Can access claim tool")) {
            com.pokecobble.Pokecobbleclaim.LOGGER.debug("Set claim tool access for " + name + ": " + categoryPermissions.get("Can access claim tool"));
        }

        // DO NOT sync to file-based storage here - let PermissionManager be the single source of truth
        // syncPermissionsToFileStorage(); // REMOVED to prevent conflicts

        // Increment data version since data has changed
        incrementDataVersion();
    }

    /**
     * Gets a specific permission value.
     *
     * @param category The category name
     * @param permission The permission name
     * @return The permission value, or false if not set
     */
    public boolean hasPermission(String category, String permission) {
        Map<String, Boolean> categoryPermissions = permissions.get(category);
        if (categoryPermissions == null) {
            com.pokecobble.Pokecobbleclaim.LOGGER.debug("Permission check for " + this.name + ": category '" + category + "' not found");
            return false;
        }
        boolean result = categoryPermissions.getOrDefault(permission, false);
        com.pokecobble.Pokecobbleclaim.LOGGER.debug("Permission check for " + this.name + ": " + category + "." + permission + " = " + result);
        return result;
    }

    /**
     * Sets a specific permission value.
     * NOTE: This method should only be called by PermissionManager to maintain consistency.
     *
     * @param category The category name
     * @param permission The permission name
     * @param value The new value
     */
    public void setPermission(String category, String permission, boolean value) {
        Map<String, Boolean> categoryPermissions = permissions.computeIfAbsent(category, k -> new HashMap<>());
        categoryPermissions.put(permission, value);

        // DO NOT sync to file-based storage here - let PermissionManager be the single source of truth
        // syncPermissionsToFileStorage(); // REMOVED to prevent conflicts

        // Increment data version since data has changed
        incrementDataVersion();
    }

    /**
     * Gets all permissions for all categories.
     *
     * @return A map of category names to permission maps
     */
    public Map<String, Map<String, Boolean>> getAllPermissions() {
        // Return a deep copy to prevent modification
        Map<String, Map<String, Boolean>> result = new HashMap<>();
        for (Map.Entry<String, Map<String, Boolean>> entry : permissions.entrySet()) {
            result.put(entry.getKey(), new HashMap<>(entry.getValue()));
        }
        return result;
    }

    /**
     * Gets the current data version of this player.
     *
     * @return The data version
     */
    public int getDataVersion() {
        return dataVersion;
    }

    /**
     * Sets the data version of this player.
     *
     * @param dataVersion The new data version
     */
    public void setDataVersion(int dataVersion) {
        this.dataVersion = dataVersion;
    }

    /**
     * Increments the data version of this player.
     *
     * @return The new data version
     */
    public int incrementDataVersion() {
        return ++dataVersion;
    }



    /**
     * Loads permissions from file-based storage if available.
     * @return true if permissions were loaded, false if no permissions found
     */
    private boolean loadPermissionsFromFileStorage() {
        try {
            // Get the town this player belongs to
            com.pokecobble.town.Town town = com.pokecobble.town.TownManager.getInstance().getPlayerTown(this.uuid);
            if (town == null) {
                com.pokecobble.Pokecobbleclaim.LOGGER.debug("Cannot load permissions - player " + this.name + " is not in a town");
                return false;
            }

            // Load permissions from file-based system
            Map<String, Map<String, Boolean>> filePermissions =
                com.pokecobble.town.permission.PermissionManager.getInstance().getPlayerPermissions(this.uuid, town.getId());

            if (filePermissions != null && !filePermissions.isEmpty()) {
                this.permissions.clear();
                this.permissions.putAll(filePermissions);
                com.pokecobble.Pokecobbleclaim.LOGGER.debug("Loaded permissions from file storage for player " + this.name);
                return true;
            }
        } catch (Exception e) {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Error loading permissions from file storage for player " + this.name + ": " + e.getMessage());
        }

        com.pokecobble.Pokecobbleclaim.LOGGER.debug("No permissions found in file storage for player " + this.name + " - will use defaults");
        return false;
    }
}
