package com.pokecobble.town.access;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Handles permission checks and access control for town-related functionality.
 */
public class TownAccessController {
    // Singleton instance
    private static TownAccessController instance;

    // Maps subcategories to permission categories and permissions
    private final Map<String, String> subcategoryToPermissionCategory = new HashMap<>();
    private final Map<String, String> subcategoryToPermission = new HashMap<>();

    /**
     * Private constructor to enforce singleton pattern.
     */
    private TownAccessController() {
        initializePermissionMappings();
    }

    /**
     * Gets the singleton instance of the TownAccessController.
     *
     * @return The TownAccessController instance
     */
    public static TownAccessController getInstance() {
        if (instance == null) {
            instance = new TownAccessController();
        }
        return instance;
    }

    /**
     * Initializes the mappings between subcategories and permissions.
     */
    private void initializePermissionMappings() {
        // Main subcategory is accessible to everyone
        subcategoryToPermissionCategory.put("Main", null);
        subcategoryToPermission.put("Main", null);

        // Players subcategory is accessible to everyone
        subcategoryToPermissionCategory.put("Players", null);
        subcategoryToPermission.put("Players", null);

        // Election subcategory is accessible to everyone
        subcategoryToPermissionCategory.put("Election", null);
        subcategoryToPermission.put("Election", null);

        // Claims subcategory requires Claim Tool permission
        subcategoryToPermissionCategory.put("Claims", "Claim Tool");
        subcategoryToPermission.put("Claims", "Can view claims");

        // Jobs subcategory is accessible to everyone
        subcategoryToPermissionCategory.put("Jobs", null);
        subcategoryToPermission.put("Jobs", null);

        // Level subcategory requires Town Level permission
        subcategoryToPermissionCategory.put("Level", "Town Level");
        subcategoryToPermission.put("Level", "Can view level");

        // Bank subcategory requires Town Bank permission
        subcategoryToPermissionCategory.put("Bank", "Town Bank");
        subcategoryToPermission.put("Bank", "Can view balance");

        // Settings subcategory is accessible to everyone
        subcategoryToPermissionCategory.put("Settings", null);
        subcategoryToPermission.put("Settings", null);
    }

    /**
     * Checks if a player has permission to access a subcategory.
     *
     * @param subcategoryName The name of the subcategory
     * @param playerTown The player's town
     * @param playerId The UUID of the player
     * @return True if the player has permission, false otherwise
     */
    public boolean hasPermissionForSubcategory(String subcategoryName, Town playerTown, UUID playerId) {
        // If the player is not in a town, they don't have permission
        if (playerTown == null || playerId == null) {
            return false;
        }

        // Get the permission category and permission name for this subcategory
        String permissionCategory = subcategoryToPermissionCategory.get(subcategoryName);
        String permissionName = subcategoryToPermission.get(subcategoryName);

        // If no permission is required, allow access
        if (permissionCategory == null || permissionName == null) {
            return true;
        }

        // Get the player's town player object
        TownPlayer townPlayer = playerTown.getPlayer(playerId);
        if (townPlayer == null) {
            return false;
        }

        // Check if the player has admin permissions (owners and admin viewers have all permissions)
        if (townPlayer.getRank().hasAdminPermissions()) {
            return true;
        }

        // Check if the player has the required permission
        return townPlayer.hasPermission(permissionCategory, permissionName);
    }

    /**
     * Gets the permission category for a subcategory.
     *
     * @param subcategoryName The name of the subcategory
     * @return The permission category, or null if no permission is required
     */
    public String getPermissionCategory(String subcategoryName) {
        return subcategoryToPermissionCategory.get(subcategoryName);
    }

    /**
     * Gets the permission name for a subcategory.
     *
     * @param subcategoryName The name of the subcategory
     * @return The permission name, or null if no permission is required
     */
    public String getPermissionName(String subcategoryName) {
        return subcategoryToPermission.get(subcategoryName);
    }
}
