package com.pokecobble.town.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;

import com.pokecobble.town.sound.SoundUtil;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;
import org.lwjgl.glfw.GLFW;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Screen for managing a player's rank and permissions in a town.
 * Redesigned with a clean, modern interface based on AdminRoleScreen design.
 */
public class PlayerManageScreen extends Screen {
    private final Screen parent;
    private final Town town;
    private TownPlayer targetPlayer;

    // Panel dimensions - modern responsive sizing
    private int panelWidth = 600;  // Wider for better content layout
    private int panelHeight = 400; // Taller for better content display
    private int leftX;
    private int topY;

    // Modern color scheme matching ModernTownScreen and MyTownScreen
    private static final int PRIMARY_BG = 0xFF2C2C2C;        // Primary background
    private static final int SECONDARY_BG = 0xFF3C3C3C;      // Secondary background
    private static final int ACCENT_PRIMARY = 0xFF3498DB;     // Primary accent blue
    private static final int ACCENT_SUCCESS = 0xFF27AE60;     // Success green
    private static final int ACCENT_WARNING = 0xFFF39C12;     // Warning orange
    private static final int ACCENT_DANGER = 0xFFE74C3C;      // Danger red
    private static final int TEXT_PRIMARY = 0xFFFFFFFF;       // Primary text
    private static final int TEXT_SECONDARY = 0xFFB0B0B0;     // Secondary text
    private static final int TEXT_MUTED = 0xFF808080;         // Muted text
    private static final int BORDER_COLOR = 0xFF333333;       // Border color
    private static final int HOVER_OVERLAY = 0x20FFFFFF;      // Hover effect

    // Glass effect colors
    private static final int GLASS_BG = 0x60404040;           // Glass background
    private static final int GLASS_BG_HOVER = 0x70505050;     // Glass background hover
    private static final int GLASS_HIGHLIGHT = 0x30FFFFFF;    // Glass highlight
    private static final int GLASS_SHADOW = 0x40000000;       // Glass shadow
    private static final int CARD_BG = 0x40303030;            // Card background
    private static final int CARD_BG_HOVER = 0x60404040;      // Card background hover

    // Modern spacing system (4px grid)
    private static final int SPACING_XS = 2;
    private static final int SPACING_SM = 4;
    private static final int SPACING_MD = 8;
    private static final int SPACING_LG = 12;
    private static final int SPACING_XL = 16;

    // Component dimensions
    private static final int ROW_HEIGHT = 18;
    private static final int CATEGORY_HEIGHT = 18; // Smaller vertical size for categories
    private static final int CATEGORY_WIDTH = 140;
    private static final int HEADER_HEIGHT = 40;

    // Scrolling support for categories
    private int categoryScrollOffset = 0;
    private int maxCategoryScroll = 0;

    // Scrolling support for permission content
    private int permissionScrollOffset = 0;
    private int maxPermissionScroll = 0;

    // Layout constants - modern dimensions
    private static final int PERMISSION_ROW_HEIGHT = 20;
    private static final int BUTTON_HEIGHT = 18;
    private static final int SHADOW_SIZE = 3;

    // Categories and permissions
    private final List<PermissionCategory> categories = new ArrayList<>();
    private int selectedCategoryIndex = 0;



    // Modern animation variables
    private float categoryHoverAnimation = 0.0f;
    private int hoveredCategoryIndex = -1;
    private float saveButtonAnimation = 0.0f;
    private boolean saveButtonHovered = false;
    private float cancelButtonAnimation = 0.0f;
    private boolean cancelButtonHovered = false;
    private long lastRenderTime = 0;
    private static final float ANIMATION_SPEED = 0.15f;

    // State tracking
    private boolean hasChanges = false;

    // Status message
    private String statusMessage = "";
    private int statusColor = TEXT_PRIMARY;
    private int statusTimer = 0;

    // Permission refresh tracking
    private int lastDataVersion = -1;
    private int refreshCheckTimer = 0;



    /**
     * Creates a new player management screen.
     * Note: Permission checking is handled by the calling screen (MyTownScreen) before creating this screen.
     *
     * @param parent The parent screen
     * @param town The town
     * @param targetPlayer The player to manage
     */
    public PlayerManageScreen(Screen parent, Town town, TownPlayer targetPlayer) {
        super(Text.literal("Manage Player"));
        this.parent = parent;
        this.town = town;
        this.targetPlayer = targetPlayer;

        // Get client instance for later use
        net.minecraft.client.MinecraftClient client = net.minecraft.client.MinecraftClient.getInstance();

        // Request the latest player data from the server for the target player
        com.pokecobble.town.network.player.PlayerNetworkHandler.requestPlayerData(targetPlayer.getUuid());

        // With the new simplified permission system, permissions are automatically synced on connection
        // No need to manually request sync

        // Setup permission categories
        setupPermissionCategories();

        // Register for player data updates (consolidated sync system)
        com.pokecobble.town.client.ClientSyncManager.getInstance().addEventListener("player_updated", this::onPlayerDataUpdated);

        // Register for permission update events
        com.pokecobble.town.client.ClientSyncManager.getInstance().addEventListener("player_permission_updated", this::onPlayerPermissionUpdated);

        // Schedule a refresh after a short delay to ensure data is loaded
        new Thread(() -> {
            try {
                Thread.sleep(500); // Wait 0.5 seconds for data to load
                if (client != null) {
                    client.execute(() -> {
                        refreshPermissionToggles();
                        Pokecobbleclaim.LOGGER.debug("Initial permission refresh completed for player " + targetPlayer.getName());
                    });
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }

    /**
     * Sets up the permission categories and initializes toggle states.
     */
    private void setupPermissionCategories() {
        categories.clear();

        // Add rank category first - filter out ADMIN_VIEWER from visible options
        List<String> rankOptions = new ArrayList<>();
        for (TownPlayerRank rank : TownPlayerRank.values()) {
            if (rank.isVisibleInPlayerList()) {
                rankOptions.add(rank.getDisplayName());
            }
        }
        int initialRankIndex = getRankIndex(targetPlayer.getRank());
        // If the player has ADMIN_VIEWER rank (not visible), default to first visible rank
        categories.add(new PermissionCategory("Rank", "Player's position in the town hierarchy", rankOptions,
                initialRankIndex >= 0 ? initialRankIndex : 0));

        // Add permission categories with toggle switches
        Map<String, Map<String, Boolean>> playerPermissions = targetPlayer.getAllPermissions();

        // Player Management permissions
        Map<String, Boolean> playerManagementPerms = playerPermissions.getOrDefault("Player Management", new HashMap<>());
        List<String> playerManagementOptions = Arrays.asList(
            "Can kick players", "Can change player ranks", "Can manage player permissions", "Can invite players"
        );
        categories.add(new PermissionCategory("Player Management", "Permissions for managing other players",
                playerManagementOptions, playerManagementPerms));

        // Job permissions
        Map<String, Boolean> jobPerms = playerPermissions.getOrDefault("Job", new HashMap<>());
        List<String> jobOptions = Arrays.asList(
            "Can request building approval", "Can view job applications", "Can apply for jobs", "Can manage town jobs"
        );
        categories.add(new PermissionCategory("Job", "Permissions for job-related activities",
                jobOptions, jobPerms));

        // Claim Tool permissions
        Map<String, Boolean> claimToolPerms = playerPermissions.getOrDefault("Claim Tool", new HashMap<>());
        List<String> claimToolOptions = Arrays.asList(
            "Can view claims", "Can access claim tool", "Can delete claims"
        );
        categories.add(new PermissionCategory("Claim Tool", "Permissions for managing town claims",
                claimToolOptions, claimToolPerms));

        // Town Bank permissions
        Map<String, Boolean> bankPerms = playerPermissions.getOrDefault("Town Bank", new HashMap<>());
        List<String> bankOptions = Arrays.asList(
            "Can view balance", "Can deposit money", "Can withdraw money", "Can view transactions"
        );
        categories.add(new PermissionCategory("Town Bank", "Permissions for town financial management",
                bankOptions, bankPerms));

        // Town Level permissions
        Map<String, Boolean> levelPerms = playerPermissions.getOrDefault("Town Level", new HashMap<>());
        List<String> levelOptions = Arrays.asList(
            "Can view level", "Can contribute resources", "Can view benefits"
        );
        categories.add(new PermissionCategory("Town Level", "Permissions for town progression",
                levelOptions, levelPerms));

        // Town Settings permissions
        Map<String, Boolean> settingsPerms = playerPermissions.getOrDefault("Town Settings", new HashMap<>());
        List<String> settingsOptions = Arrays.asList(
            "Can modify settings", "Can change town name",
            "Can change town description", "Can change town spawn"
        );
        categories.add(new PermissionCategory("Town Settings", "Permissions for town configuration",
                settingsOptions, settingsPerms));
    }

    /**
     * Handles player data updates from the consolidated sync system.
     */
    private void onPlayerDataUpdated(Object data) {
        if (data instanceof java.util.Map) {
            @SuppressWarnings("unchecked")
            java.util.Map<String, Object> eventData = (java.util.Map<String, Object>) data;

            // Check if this update is for our target player
            Object playerIdObj = eventData.get("playerId");
            if (playerIdObj instanceof java.util.UUID) {
                java.util.UUID playerId = (java.util.UUID) playerIdObj;
                if (playerId.equals(targetPlayer.getUuid())) {
                    // Refresh permissions on the next tick to avoid concurrent modification
                    this.client.execute(this::refreshPermissions);
                }
            }
        }
    }

    /**
     * Handles player permission updates from the sync system.
     */
    private void onPlayerPermissionUpdated(Object data) {
        if (data instanceof java.util.Map) {
            @SuppressWarnings("unchecked")
            java.util.Map<String, Object> eventData = (java.util.Map<String, Object>) data;

            // Check if this update is for our target player
            Object playerIdObj = eventData.get("playerId");
            if (playerIdObj instanceof java.util.UUID) {
                java.util.UUID playerId = (java.util.UUID) playerIdObj;
                if (playerId.equals(targetPlayer.getUuid())) {
                    // Refresh the permission UI on the next tick
                    this.client.execute(this::refreshPermissionUI);
                }
            }
        }
    }

    /**
     * Gets the index of a rank in the visible rank list (excluding ADMIN_VIEWER).
     */
    private int getRankIndex(TownPlayerRank rank) {
        // If the rank is ADMIN_VIEWER, it shouldn't be in the visible list
        if (!rank.isVisibleInPlayerList()) {
            return -1; // Invalid index for invisible ranks
        }

        int index = 0;
        for (TownPlayerRank r : TownPlayerRank.values()) {
            if (r.isVisibleInPlayerList()) {
                if (r == rank) {
                    return index;
                }
                index++;
            }
        }
        return -1; // Should never reach here for valid visible ranks
    }

    /**
     * Gets the TownPlayerRank from the visible rank list index.
     */
    private TownPlayerRank getRankFromIndex(int index) {
        int currentIndex = 0;
        for (TownPlayerRank rank : TownPlayerRank.values()) {
            if (rank.isVisibleInPlayerList()) {
                if (currentIndex == index) {
                    return rank;
                }
                currentIndex++;
            }
        }
        return null; // Invalid index
    }

    @Override
    protected void init() {
        super.init();

        // Initialize modern animations
        categoryHoverAnimation = 0.0f;
        hoveredCategoryIndex = -1;
        saveButtonAnimation = 0.0f;
        saveButtonHovered = false;
        cancelButtonAnimation = 0.0f;
        cancelButtonHovered = false;
        lastRenderTime = System.currentTimeMillis();

        // Modern responsive panel sizing
        panelWidth = Math.min(700, width - 40);  // Wider for better layout
        panelHeight = Math.min(500, height - 40); // Taller for better content
        leftX = (width - panelWidth) / 2;
        topY = (height - panelHeight) / 2;
    }

    @Override
    public void tick() {
        super.tick();

        // Update status timer
        if (statusTimer > 0) {
            statusTimer--;
        }

        // Check for permission updates every 10 ticks (0.5 seconds)
        refreshCheckTimer++;
        if (refreshCheckTimer >= 10) {
            refreshCheckTimer = 0;
            checkForPermissionUpdates();
        }
    }

    /**
     * Checks if the target player's data has been updated and refreshes permissions if needed.
     */
    private void checkForPermissionUpdates() {
        if (town != null) {
            TownPlayer updatedPlayer = town.getPlayer(targetPlayer.getUuid());
            if (updatedPlayer != null && updatedPlayer.getDataVersion() != lastDataVersion) {
                lastDataVersion = updatedPlayer.getDataVersion();
                refreshPermissions();
            }
        }
    }



    @Override
    public void close() {
        // Clean up event listeners
        com.pokecobble.town.client.ClientSyncManager.getInstance()
            .removeEventListener("player_updated", this::onPlayerDataUpdated);
        com.pokecobble.town.client.ClientSyncManager.getInstance()
            .removeEventListener("player_permission_updated", this::onPlayerPermissionUpdated);
        super.close();
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Update modern animations
        long currentTime = System.currentTimeMillis();
        float deltaTime = (currentTime - lastRenderTime) / 1000.0f;
        lastRenderTime = currentTime;

        updateAnimations(mouseX, mouseY, deltaTime);

        // Draw modern background
        this.renderBackground(context);

        // Draw modern panel with shadow effect
        drawModernPanel(context, leftX, topY, panelWidth, panelHeight);

        // Draw modern header
        drawModernHeader(context, leftX, topY, panelWidth);

        // Calculate content area - extend closer to bottom
        int contentY = topY + HEADER_HEIGHT + SPACING_MD;
        int contentHeight = panelHeight - HEADER_HEIGHT - SPACING_MD - 25; // Reduced space for buttons to extend content area

        // Draw category sidebar
        drawCategorySidebar(context, mouseX, mouseY, leftX + SPACING_MD, contentY, CATEGORY_WIDTH, contentHeight);

        // Draw permission content area
        int contentX = leftX + SPACING_MD + CATEGORY_WIDTH + SPACING_MD;
        int contentWidth = panelWidth - SPACING_MD * 3 - CATEGORY_WIDTH;
        drawPermissionContent(context, mouseX, mouseY, contentX, contentY, contentWidth, contentHeight);

        // Draw modern action buttons
        drawActionButtons(context, mouseX, mouseY);

        // Draw status message if present
        if (statusTimer > 0) {
            context.drawCenteredTextWithShadow(this.textRenderer, statusMessage,
                leftX + panelWidth / 2, topY + panelHeight - 15, statusColor);
        }
    }

    /**
     * Updates all animations based on mouse position and time delta.
     */
    private void updateAnimations(int mouseX, int mouseY, float deltaTime) {
        // Update category hover animations with scrolling support
        boolean anyHovered = false;
        int contentY = topY + HEADER_HEIGHT + SPACING_MD;
        int contentHeight = panelHeight - HEADER_HEIGHT - SPACING_MD - 25; // Updated to match new content height

        // Check if mouse is within category sidebar bounds
        if (mouseX >= leftX + SPACING_MD && mouseX <= leftX + SPACING_MD + CATEGORY_WIDTH &&
            mouseY >= contentY && mouseY <= contentY + contentHeight) {

            int categoryY = contentY + SPACING_MD - categoryScrollOffset;

            for (int i = 0; i < categories.size(); i++) {
                // Check if this category is visible and hovered
                if (categoryY + CATEGORY_HEIGHT >= contentY && categoryY <= contentY + contentHeight &&
                    mouseY >= categoryY && mouseY <= categoryY + CATEGORY_HEIGHT) {

                    hoveredCategoryIndex = i;
                    anyHovered = true;
                    break;
                }

                categoryY += CATEGORY_HEIGHT + SPACING_SM;
            }
        }

        if (!anyHovered) {
            hoveredCategoryIndex = -1;
        }

        // Update category hover animation
        if (hoveredCategoryIndex != -1) {
            categoryHoverAnimation = Math.min(1.0f, categoryHoverAnimation + ANIMATION_SPEED);
        } else {
            categoryHoverAnimation = Math.max(0.0f, categoryHoverAnimation - ANIMATION_SPEED);
        }

        // Update button hover states and animations
        updateButtonAnimations(mouseX, mouseY, deltaTime);
    }

    /**
     * Updates button animations.
     */
    private void updateButtonAnimations(int mouseX, int mouseY, float deltaTime) {
        // Calculate button positions
        int buttonY = topY + panelHeight - 25; // Updated to match new button position
        int saveButtonX = leftX + panelWidth - 160;
        int cancelButtonX = leftX + panelWidth - 80;
        int buttonWidth = 70;
        int buttonHeight = 18; // Updated to match new button height

        // Check save button hover
        boolean saveHovered = mouseX >= saveButtonX && mouseX <= saveButtonX + buttonWidth &&
                             mouseY >= buttonY && mouseY <= buttonY + buttonHeight;
        saveButtonHovered = saveHovered;

        // Check cancel button hover
        boolean cancelHovered = mouseX >= cancelButtonX && mouseX <= cancelButtonX + buttonWidth &&
                               mouseY >= buttonY && mouseY <= buttonY + buttonHeight;
        cancelButtonHovered = cancelHovered;

        // Update animations
        if (saveButtonHovered) {
            saveButtonAnimation = Math.min(1.0f, saveButtonAnimation + ANIMATION_SPEED);
        } else {
            saveButtonAnimation = Math.max(0.0f, saveButtonAnimation - ANIMATION_SPEED);
        }

        if (cancelButtonHovered) {
            cancelButtonAnimation = Math.min(1.0f, cancelButtonAnimation + ANIMATION_SPEED);
        } else {
            cancelButtonAnimation = Math.max(0.0f, cancelButtonAnimation - ANIMATION_SPEED);
        }
    }

    /**
     * Draws the main modern panel with glass effects.
     */
    private void drawModernPanel(DrawContext context, int x, int y, int width, int height) {
        // Draw main background with glass effect
        context.fill(x, y, x + width, y + height, GLASS_BG);

        // Draw subtle border
        context.fill(x, y, x + width, y + 1, GLASS_HIGHLIGHT);
        context.fill(x, y, x + 1, y + height, GLASS_HIGHLIGHT);
        context.fill(x + width - 1, y, x + width, y + height, GLASS_SHADOW);
        context.fill(x, y + height - 1, x + width, y + height, GLASS_SHADOW);
    }

    /**
     * Draws the modern header with title and controls.
     */
    private void drawModernHeader(DrawContext context, int x, int y, int width) {
        // Draw header background
        context.fill(x, y, x + width, y + HEADER_HEIGHT, GLASS_HIGHLIGHT);

        // Draw header title
        String title = "Player Permissions - " + this.targetPlayer.getName();
        context.drawTextWithShadow(this.textRenderer, title,
            x + SPACING_MD, y + (HEADER_HEIGHT - 9) / 2, TEXT_PRIMARY);

        // Draw close button
        int closeButtonX = x + width - 20;
        int closeButtonY = y + (HEADER_HEIGHT - 16) / 2;
        context.drawTextWithShadow(this.textRenderer, "×",
            closeButtonX, closeButtonY, TEXT_SECONDARY);
    }

    /**
     * Draws the category sidebar with modern styling and proper bounds checking.
     */
    private void drawCategorySidebar(DrawContext context, int mouseX, int mouseY, int x, int y, int width, int height) {
        // Draw sidebar background
        context.fill(x, y, x + width, y + height, GLASS_SHADOW);

        // Calculate available space for categories
        int availableHeight = height - SPACING_MD * 2;
        int totalCategoriesHeight = categories.size() * (CATEGORY_HEIGHT + SPACING_SM) - SPACING_SM;

        // Update max scroll offset
        maxCategoryScroll = Math.max(0, totalCategoriesHeight - availableHeight);

        // Clamp scroll offset
        categoryScrollOffset = Math.max(0, Math.min(categoryScrollOffset, maxCategoryScroll));

        // Enable scissor test to clip content to sidebar bounds
        context.enableScissor(x, y, x + width, y + height);

        int categoryY = y + SPACING_MD - categoryScrollOffset;

        for (int i = 0; i < categories.size(); i++) {
            PermissionCategory category = categories.get(i);
            boolean isSelected = i == selectedCategoryIndex;
            boolean isHovered = i == hoveredCategoryIndex;

            // Only draw categories that are visible within the bounds
            if (categoryY + CATEGORY_HEIGHT >= y && categoryY <= y + height) {
                // Calculate colors with animation
                int bgColor = GLASS_BG;
                if (isSelected) {
                    bgColor = ACCENT_PRIMARY;
                } else if (isHovered) {
                    float alpha = categoryHoverAnimation;
                    bgColor = blendColors(GLASS_BG, GLASS_HIGHLIGHT, alpha);
                }

                // Draw category background
                context.fill(x + SPACING_XS, categoryY, x + width - SPACING_XS,
                    categoryY + CATEGORY_HEIGHT, bgColor);

                // Draw category text
                String icon = getCategoryIcon(category.name);
                String text = icon + " " + category.name;
                int textColor = isSelected ? TEXT_PRIMARY : TEXT_SECONDARY;

                context.drawTextWithShadow(this.textRenderer, text,
                    x + SPACING_MD, categoryY + (CATEGORY_HEIGHT - 9) / 2, textColor);
            }

            categoryY += CATEGORY_HEIGHT + SPACING_SM;
        }

        // Disable scissor test
        context.disableScissor();

        // Draw scroll indicators if needed
        if (maxCategoryScroll > 0) {
            drawScrollIndicators(context, x, y, width, height);
        }
    }

    /**
     * Draws scroll indicators for the category sidebar.
     */
    private void drawScrollIndicators(DrawContext context, int x, int y, int width, int height) {
        // Draw scroll track
        int trackWidth = 4;
        int trackX = x + width - trackWidth - 2;
        context.fill(trackX, y + 2, trackX + trackWidth, y + height - 2, GLASS_SHADOW);

        // Calculate scroll thumb position and size
        float scrollRatio = (float) categoryScrollOffset / maxCategoryScroll;
        int availableHeight = height - 4;
        int thumbHeight = Math.max(10, (int) (availableHeight * 0.3f)); // Minimum 10px thumb
        int thumbY = y + 2 + (int) (scrollRatio * (availableHeight - thumbHeight));

        // Draw scroll thumb
        context.fill(trackX, thumbY, trackX + trackWidth, thumbY + thumbHeight, TEXT_SECONDARY);
    }







    /**
     * Draws the permission content area with scrolling support.
     */
    private void drawPermissionContent(DrawContext context, int mouseX, int mouseY, int x, int y, int width, int height) {
        if (selectedCategoryIndex < 0 || selectedCategoryIndex >= categories.size()) return;

        PermissionCategory selectedCategory = categories.get(selectedCategoryIndex);

        // Draw content background
        context.fill(x, y, x + width, y + height, GLASS_BG);

        // Draw category title
        context.drawTextWithShadow(this.textRenderer, selectedCategory.name,
            x + SPACING_MD, y + SPACING_MD, TEXT_PRIMARY);

        // Calculate scrollable content area
        int titleHeight = 20;
        int scrollableY = y + SPACING_MD + titleHeight;
        int scrollableHeight = height - SPACING_MD - titleHeight;

        // Calculate total content height for scrolling
        int totalContentHeight = 0;
        if (selectedCategory.name.equals("Rank")) {
            totalContentHeight = selectedCategory.options.size() * (ROW_HEIGHT + SPACING_XS) - SPACING_XS;
        } else if (selectedCategory.isToggleCategory) {
            totalContentHeight = selectedCategory.options.size() * (ROW_HEIGHT + SPACING_XS) - SPACING_XS;
        }

        // Update scroll limits
        maxPermissionScroll = Math.max(0, totalContentHeight - scrollableHeight + SPACING_MD * 2);
        permissionScrollOffset = Math.max(0, Math.min(permissionScrollOffset, maxPermissionScroll));

        // Enable scissor test to clip content to scrollable area
        context.enableScissor(x, scrollableY, x + width, scrollableY + scrollableHeight);

        // Draw content based on category type with scroll offset
        int contentY = scrollableY - permissionScrollOffset;

        if (selectedCategory.name.equals("Rank")) {
            drawRankSelection(context, mouseX, mouseY, x + SPACING_MD, contentY, width - SPACING_MD * 2, selectedCategory);
        } else if (selectedCategory.isToggleCategory) {
            drawPermissionToggles(context, mouseX, mouseY, x + SPACING_MD, contentY, width - SPACING_MD * 2, selectedCategory);
        }

        context.disableScissor();

        // Draw scroll indicators if needed
        if (maxPermissionScroll > 0) {
            drawPermissionScrollIndicators(context, x, scrollableY, width, scrollableHeight);
        }
    }

    /**
     * Draws scroll indicators for the permission content area.
     */
    private void drawPermissionScrollIndicators(DrawContext context, int x, int y, int width, int height) {
        int scrollBarWidth = 4;
        int scrollBarX = x + width - scrollBarWidth - 2;

        // Draw scroll track
        context.fill(scrollBarX, y, scrollBarX + scrollBarWidth, y + height, GLASS_SHADOW);

        // Calculate scroll thumb position and size
        if (maxPermissionScroll > 0) {
            float scrollRatio = (float) permissionScrollOffset / maxPermissionScroll;
            float thumbRatio = (float) height / (height + maxPermissionScroll);

            int thumbHeight = Math.max(10, (int) (height * thumbRatio));
            int thumbY = y + (int) ((height - thumbHeight) * scrollRatio);

            // Draw scroll thumb
            context.fill(scrollBarX, thumbY, scrollBarX + scrollBarWidth, thumbY + thumbHeight, TEXT_SECONDARY);
        }

        // Draw scroll arrows if needed
        if (permissionScrollOffset > 0) {
            // Up arrow indicator
            context.drawTextWithShadow(this.textRenderer, "▲", scrollBarX - 6, y + 2, TEXT_SECONDARY);
        }
        if (permissionScrollOffset < maxPermissionScroll) {
            // Down arrow indicator
            context.drawTextWithShadow(this.textRenderer, "▼", scrollBarX - 6, y + height - 10, TEXT_SECONDARY);
        }
    }

    /**
     * Draws rank selection options.
     */
    private void drawRankSelection(DrawContext context, int mouseX, int mouseY, int x, int y, int width, PermissionCategory category) {
        int optionY = y;

        for (int i = 0; i < category.options.size(); i++) {
            String rankName = category.options.get(i).toString();
            boolean isSelected = i == category.selectedOption;
            boolean isHovered = mouseX >= x && mouseX <= x + width &&
                               mouseY >= optionY && mouseY <= optionY + ROW_HEIGHT;

            // Draw option background
            int bgColor = GLASS_BG;
            if (isSelected) {
                bgColor = ACCENT_PRIMARY;
            } else if (isHovered) {
                bgColor = GLASS_HIGHLIGHT;
            }

            context.fill(x, optionY, x + width, optionY + ROW_HEIGHT, bgColor);

            // Draw rank name
            int textColor = isSelected ? TEXT_PRIMARY : TEXT_SECONDARY;
            context.drawTextWithShadow(this.textRenderer, rankName,
                x + SPACING_MD, optionY + (ROW_HEIGHT - 9) / 2, textColor);

            // Draw selection indicator
            if (isSelected) {
                context.drawTextWithShadow(this.textRenderer, "✓",
                    x + width - 20, optionY + (ROW_HEIGHT - 9) / 2, ACCENT_SUCCESS);
            }

            optionY += ROW_HEIGHT + SPACING_XS;
        }
    }

    /**
     * Draws permission toggle options.
     */
    private void drawPermissionToggles(DrawContext context, int mouseX, int mouseY, int x, int y, int width, PermissionCategory category) {
        int optionY = y;

        for (int i = 0; i < category.options.size(); i++) {
            PermissionOption option = category.options.get(i);
            String permissionName = option.toString();
            boolean isToggled = option.toggleState;
            boolean isHovered = mouseX >= x && mouseX <= x + width &&
                               mouseY >= optionY && mouseY <= optionY + ROW_HEIGHT;

            // Draw option background
            int bgColor = isHovered ? GLASS_HIGHLIGHT : GLASS_BG;
            context.fill(x, optionY, x + width, optionY + ROW_HEIGHT, bgColor);

            // Draw permission name
            context.drawTextWithShadow(this.textRenderer, permissionName,
                x + SPACING_MD, optionY + (ROW_HEIGHT - 9) / 2, TEXT_PRIMARY);

            // Draw toggle switch
            int toggleWidth = 40;
            int toggleHeight = 14;
            int toggleX = x + width - toggleWidth - SPACING_MD;
            int toggleY = optionY + (ROW_HEIGHT - toggleHeight) / 2;

            int toggleColor = isToggled ? ACCENT_SUCCESS : GLASS_SHADOW;
            context.fill(toggleX, toggleY, toggleX + toggleWidth, toggleY + toggleHeight, toggleColor);

            // Draw toggle text
            String toggleText = isToggled ? "ON" : "OFF";
            int textColor = isToggled ? TEXT_PRIMARY : TEXT_SECONDARY;
            int textX = toggleX + (toggleWidth - this.textRenderer.getWidth(toggleText)) / 2;
            context.drawTextWithShadow(this.textRenderer, toggleText,
                textX, toggleY + (toggleHeight - 9) / 2, textColor);

            optionY += ROW_HEIGHT + SPACING_XS;
        }
    }

    /**
     * Draws modern action buttons.
     */
    private void drawActionButtons(DrawContext context, int mouseX, int mouseY) {
        int buttonY = topY + panelHeight - 25; // Moved up to accommodate larger content area
        int buttonWidth = 70;
        int buttonHeight = 18; // Slightly smaller to fit in reduced space

        // Save button
        int saveButtonX = leftX + panelWidth - 160;
        int saveColor = hasChanges ? ACCENT_SUCCESS : GLASS_SHADOW;
        if (saveButtonHovered) {
            saveColor = hasChanges ? lightenColor(ACCENT_SUCCESS, 20) : GLASS_HIGHLIGHT;
        }

        drawModernButton(context, saveButtonX, buttonY, buttonWidth, buttonHeight, saveColor, "Save");

        // Cancel button
        int cancelButtonX = leftX + panelWidth - 80;
        int cancelColor = ACCENT_DANGER;
        if (cancelButtonHovered) {
            cancelColor = lightenColor(ACCENT_DANGER, 20);
        }

        drawModernButton(context, cancelButtonX, buttonY, buttonWidth, buttonHeight, cancelColor, "Cancel");
    }

    /**
     * Draws a modern button with glass effects.
     */
    private void drawModernButton(DrawContext context, int x, int y, int width, int height, int color, String text) {
        // Draw button background
        context.fill(x, y, x + width, y + height, color);

        // Draw button border
        context.fill(x, y, x + width, y + 1, GLASS_HIGHLIGHT);
        context.fill(x, y, x + 1, y + height, GLASS_HIGHLIGHT);
        context.fill(x + width - 1, y, x + width, y + height, GLASS_SHADOW);
        context.fill(x, y + height - 1, x + width, y + height, GLASS_SHADOW);

        // Draw button text
        int textX = x + (width - this.textRenderer.getWidth(text)) / 2;
        int textY = y + (height - 9) / 2;
        context.drawTextWithShadow(this.textRenderer, text, textX, textY, TEXT_PRIMARY);
    }

    /**
     * Blends two colors with the given alpha.
     */
    private int blendColors(int color1, int color2, float alpha) {
        alpha = Math.max(0.0f, Math.min(1.0f, alpha));

        int a1 = (color1 >> 24) & 0xFF;
        int r1 = (color1 >> 16) & 0xFF;
        int g1 = (color1 >> 8) & 0xFF;
        int b1 = color1 & 0xFF;

        int a2 = (color2 >> 24) & 0xFF;
        int r2 = (color2 >> 16) & 0xFF;
        int g2 = (color2 >> 8) & 0xFF;
        int b2 = color2 & 0xFF;

        int a = (int) (a1 + (a2 - a1) * alpha);
        int r = (int) (r1 + (r2 - r1) * alpha);
        int g = (int) (g1 + (g2 - g1) * alpha);
        int b = (int) (b1 + (b2 - b1) * alpha);

        return (a << 24) | (r << 16) | (g << 8) | b;
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0) { // Left click
            // Check if clicked on a category - use proper coordinates and scrolling
            int categoryX = leftX + SPACING_MD;
            int categoryWidth = CATEGORY_WIDTH;
            int contentY = topY + HEADER_HEIGHT + SPACING_MD;
            int contentHeight = panelHeight - HEADER_HEIGHT - SPACING_MD - 25; // Updated space for buttons

            // Check if click is within category sidebar bounds
            if (mouseX >= categoryX && mouseX <= categoryX + categoryWidth &&
                mouseY >= contentY && mouseY <= contentY + contentHeight) {

                int categoryY = contentY + SPACING_MD - categoryScrollOffset;

                for (int i = 0; i < categories.size(); i++) {
                    // Check if this category is visible and clicked
                    if (categoryY + CATEGORY_HEIGHT >= contentY && categoryY <= contentY + contentHeight &&
                        mouseY >= categoryY && mouseY <= categoryY + CATEGORY_HEIGHT) {

                        // Select this category
                        selectedCategoryIndex = i;
                        playClickSound();
                        return true;
                    }

                    categoryY += CATEGORY_HEIGHT + SPACING_SM;
                }
            }

            // Get content area dimensions - use proper coordinates
            int permissionContentY = contentY;
            int contentX = leftX + SPACING_MD + CATEGORY_WIDTH + SPACING_MD;
            int permissionContentWidth = panelWidth - SPACING_MD * 3 - CATEGORY_WIDTH;

            // Get selected category
            PermissionCategory selectedCategory = categories.get(selectedCategoryIndex);

            // Check if clicked on permission content area (with scrolling support)
            int scrollableY = permissionContentY + 20; // Account for title
            int scrollableHeight = contentHeight - 20;

            if (mouseX >= contentX && mouseX <= contentX + permissionContentWidth &&
                mouseY >= scrollableY && mouseY <= scrollableY + scrollableHeight) {

                // Check if clicked on rank options
                if (selectedCategory.name.equals("Rank")) {
                    int rankY = scrollableY - permissionScrollOffset;

                    for (int i = 0; i < selectedCategory.options.size(); i++) {
                        if (mouseX >= contentX + 5 && mouseX <= contentX + permissionContentWidth - 5 &&
                            mouseY >= rankY && mouseY <= rankY + ROW_HEIGHT &&
                            rankY + ROW_HEIGHT >= scrollableY && rankY <= scrollableY + scrollableHeight) {
                            // Select this rank
                            selectedCategory.selectedOption = i;
                            playClickSound();
                            return true;
                        }

                        rankY += ROW_HEIGHT + SPACING_XS;
                    }
                } else if (selectedCategory.isToggleCategory) {
                    // Check if clicked on permission toggles
                    int permissionY = scrollableY - permissionScrollOffset;

                    for (int i = 0; i < selectedCategory.options.size(); i++) {
                        PermissionOption option = selectedCategory.options.get(i);

                        // Only process if the item is visible
                        if (permissionY + ROW_HEIGHT >= scrollableY && permissionY <= scrollableY + scrollableHeight) {
                            // Calculate toggle button position
                            int toggleWidth = 40;
                            int toggleHeight = 14;
                            int toggleX = contentX + permissionContentWidth - toggleWidth - 10;

                            // Check if clicked on the toggle switch
                            if (mouseX >= toggleX && mouseX <= toggleX + toggleWidth &&
                                mouseY >= permissionY + 1 && mouseY <= permissionY + 1 + toggleHeight) {
                                // Toggle the permission
                                option.toggleState = !option.toggleState;
                                playClickSound();
                                return true;
                            }
                        }

                        permissionY += ROW_HEIGHT + SPACING_XS;
                    }
                }
            }

            // Check save and cancel buttons - use same coordinates as drawing code
            int buttonY = topY + panelHeight - 25; // Match drawActionButtons()
            int buttonWidth = 70; // Match drawActionButtons()
            int buttonHeight = 18; // Match drawActionButtons()
            int saveButtonX = leftX + panelWidth - 160; // Match drawActionButtons()
            int cancelButtonX = leftX + panelWidth - 80; // Match drawActionButtons()

            // Debug button click zones
            System.out.println("DEBUG BUTTON CLICK: mouseX=" + mouseX + ", mouseY=" + mouseY);
            System.out.println("  Save button: X=" + saveButtonX + "-" + (saveButtonX + buttonWidth) + ", Y=" + buttonY + "-" + (buttonY + buttonHeight));
            System.out.println("  Cancel button: X=" + cancelButtonX + "-" + (cancelButtonX + buttonWidth) + ", Y=" + buttonY + "-" + (buttonY + buttonHeight));

            // Check save button
            if (mouseX >= saveButtonX && mouseX <= saveButtonX + buttonWidth &&
                mouseY >= buttonY && mouseY <= buttonY + buttonHeight) {
                System.out.println("DEBUG: Save button clicked!");
                // Save changes
                saveChanges();
                playClickSound();
                return true;
            }

            // Check cancel button (was "back button")
            if (mouseX >= cancelButtonX && mouseX <= cancelButtonX + buttonWidth &&
                mouseY >= buttonY && mouseY <= buttonY + buttonHeight) {
                System.out.println("DEBUG: Cancel button clicked!");
                // Return to parent screen
                this.client.setScreen(parent);
                playClickSound();
                return true;
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        // Check if scrolling over category sidebar
        int categoryX = leftX + SPACING_MD;
        int categoryWidth = CATEGORY_WIDTH;
        int contentY = topY + HEADER_HEIGHT + SPACING_MD;
        int contentHeight = panelHeight - HEADER_HEIGHT - SPACING_MD - 25; // Updated content height

        if (mouseX >= categoryX && mouseX <= categoryX + categoryWidth &&
            mouseY >= contentY && mouseY <= contentY + contentHeight) {

            // Only scroll if there's content to scroll
            if (maxCategoryScroll > 0) {
                int scrollAmount = (int) (amount * 20); // Scroll speed
                categoryScrollOffset = Math.max(0, Math.min(maxCategoryScroll,
                    categoryScrollOffset - scrollAmount));
                return true;
            }
        }

        // Check if scrolling over permission content area
        int permissionX = leftX + SPACING_MD + CATEGORY_WIDTH + SPACING_MD;
        int permissionWidth = panelWidth - SPACING_MD * 3 - CATEGORY_WIDTH;

        if (mouseX >= permissionX && mouseX <= permissionX + permissionWidth &&
            mouseY >= contentY && mouseY <= contentY + contentHeight) {

            // Only scroll if there's content to scroll
            if (maxPermissionScroll > 0) {
                int scrollAmount = (int) (amount * 20); // Scroll speed
                permissionScrollOffset = Math.max(0, Math.min(maxPermissionScroll,
                    permissionScrollOffset - scrollAmount));
                return true;
            }
        }

        return super.mouseScrolled(mouseX, mouseY, amount);
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        if (keyCode == GLFW.GLFW_KEY_ESCAPE) {
            // Return to parent screen
            this.client.setScreen(parent);
            return true;
        }

        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    /**
     * Saves the rank and permission changes for the player.
     */
    private void saveChanges() {
        // Check if the current player has permission to modify ranks and permissions
        boolean canModifyRanks = false;
        boolean canModifyPermissions = false;
        if (client.player != null && town != null) {
            TownPlayer currentTownPlayer = town.getPlayer(client.player.getUuid());
            if (currentTownPlayer != null) {
                // Only owners and admin viewers can modify ranks
                canModifyRanks = currentTownPlayer.getRank().hasAdminPermissions();
                // Admins and owners can modify permissions
                canModifyPermissions = currentTownPlayer.getRank().hasAdminPermissions() ||
                                     currentTownPlayer.hasPermission("Player Management", "Can manage player permissions");
            }
        }

        boolean hasChanges = false;

        // Handle rank changes
        PermissionCategory rankCategory = categories.get(0); // Rank is always the first category
        TownPlayerRank selectedRank = getRankFromIndex(rankCategory.selectedOption);

        if (selectedRank != null && selectedRank != targetPlayer.getRank()) {
            if (!canModifyRanks) {
                NotificationManager.getInstance().addErrorNotification("You don't have permission to change player ranks.");
                return;
            }

            // Check if trying to transfer mayor status
            if (selectedRank == TownPlayerRank.OWNER && selectedRank != targetPlayer.getRank()) {
                // This is a mayor transfer attempt - show confirmation dialog
                showMayorTransferConfirmation(selectedRank);
                return; // Don't continue with normal save process
            }

            // Send rank change via town settings system
            Map<String, Object> rankUpdate = new HashMap<>();
            rankUpdate.put("playerRank_" + targetPlayer.getUuid().toString(), selectedRank.name());

            com.pokecobble.config.ConfigSynchronizer.sendConfigUpdate(
                com.pokecobble.config.ConfigSynchronizer.CATEGORY_TOWN, rankUpdate);

            hasChanges = true;
        }

        // Handle permission changes
        for (int i = 1; i < categories.size(); i++) { // Skip rank category (index 0)
            PermissionCategory category = categories.get(i);
            if (category.isToggleCategory) {
                String categoryName = category.name;

                for (PermissionOption option : category.options) {
                    String permissionName = option.name;
                    boolean newValue = option.toggleState;
                    boolean currentValue = targetPlayer.hasPermission(categoryName, permissionName);

                    if (newValue != currentValue) {
                        if (!canModifyPermissions) {
                            NotificationManager.getInstance().addErrorNotification("You don't have permission to change player permissions.");
                            return;
                        }

                        // Send permission update to server using the new simplified system
                        com.pokecobble.town.client.ClientPermissionManager.getInstance().sendPermissionChange(
                            targetPlayer.getUuid(),
                            categoryName,
                            permissionName,
                            newValue
                        );

                        hasChanges = true;
                    }
                }
            }
        }

        if (hasChanges) {
            // Show success message
            NotificationManager.getInstance().addSuccessNotification("Changes saved successfully!");
            setStatus("Changes saved!", 0xFF00FF00);

            // Return to MyTownScreen after a short delay to show the success message
            new Thread(() -> {
                try {
                    Thread.sleep(1000); // Wait 1 second to show the success message
                    if (client != null) {
                        client.execute(() -> {
                            this.client.setScreen(parent);
                        });
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }).start();
        } else {
            // No changes needed
            NotificationManager.getInstance().addInfoNotification("No changes to save.");

            // Return to MyTownScreen immediately if no changes
            this.client.setScreen(parent);
        }

        // Play success sound
        playSuccessSound();
    }

    /**
     * Sets a status message to display.
     */
    private void setStatus(String message, int color) {
        this.statusMessage = message;
        this.statusColor = color;
        this.statusTimer = 60; // Show for 3 seconds (60 ticks)
    }

    /**
     * Shows a confirmation dialog for mayor transfer.
     */
    private void showMayorTransferConfirmation(TownPlayerRank newRank) {
        String title = "Transfer Mayor Status";
        String message = String.format(
            "Are you sure you want to transfer mayor status to %s?\n\n" +
            "This will:\n" +
            "• Remove your mayor privileges\n" +
            "• Make %s the new mayor\n" +
            "• Give them full control of the town\n\n" +
            "This action cannot be undone!",
            targetPlayer.getName(),
            targetPlayer.getName()
        );

        ConfirmationScreen confirmationScreen = new ConfirmationScreen(
            this,
            title,
            message,
            () -> {
                // User confirmed - proceed with mayor transfer
                performMayorTransfer(newRank);
            },
            () -> {
                // User cancelled - reset rank selection
                resetRankSelection();
            }
        );

        this.client.setScreen(confirmationScreen);
    }

    /**
     * Resets the rank selection to the current player's rank.
     */
    private void resetRankSelection() {
        // Reset the rank category selection to the current player's rank
        PermissionCategory rankCategory = categories.get(0); // Rank is always the first category
        rankCategory.selectedOption = targetPlayer.getRank().ordinal();
    }

    /**
     * Performs the mayor transfer after confirmation.
     */
    private void performMayorTransfer(TownPlayerRank newRank) {
        if (town == null || client.player == null) {
            NotificationManager.getInstance().addErrorNotification("Unable to transfer mayor status - town or player not found");
            return;
        }

        // Find the current mayor
        TownPlayer currentMayor = null;
        for (java.util.UUID playerId : town.getPlayerIds()) {
            TownPlayer player = town.getPlayer(playerId);
            if (player != null && player.getRank() == TownPlayerRank.OWNER) {
                currentMayor = player;
                break;
            }
        }

        if (currentMayor == null) {
            NotificationManager.getInstance().addErrorNotification("Current mayor not found");
            return;
        }

        // Check if the current player is the mayor (only mayors can transfer their status)
        if (!currentMayor.getUuid().equals(client.player.getUuid())) {
            NotificationManager.getInstance().addErrorNotification("Only the current mayor can transfer mayor status");
            resetRankSelection();
            return;
        }

        try {
            // Send mayor transfer via town settings system
            Map<String, Object> mayorTransfer = new HashMap<>();
            mayorTransfer.put("playerRank_" + currentMayor.getUuid().toString(), TownPlayerRank.ADMIN.name());
            mayorTransfer.put("playerRank_" + targetPlayer.getUuid().toString(), TownPlayerRank.OWNER.name());

            com.pokecobble.config.ConfigSynchronizer.sendConfigUpdate(
                com.pokecobble.config.ConfigSynchronizer.CATEGORY_TOWN, mayorTransfer);

            // Show success message
            NotificationManager.getInstance().addSuccessNotification(
                String.format("Mayor status transferred to %s successfully!", targetPlayer.getName()));

            // Set status message
            setStatus("Mayor transfer completed!", 0xFF00FF00);

            // Play success sound
            playSuccessSound();

        } catch (Exception e) {
            // If anything goes wrong, try to revert changes
            NotificationManager.getInstance().addErrorNotification("Error during mayor transfer: " + e.getMessage());
            resetRankSelection();
        }
    }



    /**
     * Gets the target player being managed.
     *
     * @return The target player
     */
    public TownPlayer getTargetPlayer() {
        return targetPlayer;
    }

    /**
     * Refreshes the permission data from the target player.
     * This should be called when updated player data is received from the server.
     */
    public void refreshPermissions() {
        // Update the target player reference from the town
        if (town != null) {
            TownPlayer updatedPlayer = town.getPlayer(targetPlayer.getUuid());
            if (updatedPlayer != null) {
                this.targetPlayer = updatedPlayer;
            }
        }

        // Update the rank category selection to reflect the current rank
        if (!categories.isEmpty()) {
            PermissionCategory rankCategory = categories.get(0); // Rank is always the first category
            int rankIndex = getRankIndex(targetPlayer.getRank());
            // If the player has ADMIN_VIEWER rank (not visible), default to first visible rank
            rankCategory.selectedOption = rankIndex >= 0 ? rankIndex : 0;
        }

        // Also refresh permission toggles
        refreshPermissionToggles();
    }

    /**
     * Refreshes the permission UI when permission data is updated.
     */
    private void refreshPermissionUI() {
        // Update the target player reference from the town
        if (town != null) {
            TownPlayer updatedPlayer = town.getPlayer(targetPlayer.getUuid());
            if (updatedPlayer != null) {
                this.targetPlayer = updatedPlayer;

                // Update the rank category selection to reflect the current rank
                if (!categories.isEmpty()) {
                    PermissionCategory rankCategory = categories.get(0); // Rank is always the first category
                    int rankIndex = getRankIndex(targetPlayer.getRank());
                    // If the player has ADMIN_VIEWER rank (not visible), default to first visible rank
                    rankCategory.selectedOption = rankIndex >= 0 ? rankIndex : 0;
                }

                // Refresh all permission toggle states from the updated player data
                refreshPermissionToggles();

                Pokecobbleclaim.LOGGER.debug("Refreshed permission UI for player " + targetPlayer.getName());
            }
        }
    }

    /**
     * Refreshes all permission toggle states from the current player data.
     */
    private void refreshPermissionToggles() {
        if (targetPlayer == null) {
            return;
        }

        // Get the latest permissions from the target player
        Map<String, Map<String, Boolean>> playerPermissions = targetPlayer.getAllPermissions();

        // Update permission categories (skip rank category at index 0)
        for (int i = 1; i < categories.size(); i++) {
            PermissionCategory category = categories.get(i);
            if (category.isToggleCategory) {
                String categoryName = category.name;
                Map<String, Boolean> categoryPermissions = playerPermissions.getOrDefault(categoryName, new HashMap<>());

                // Update each permission toggle in this category
                for (PermissionOption option : category.options) {
                    boolean currentValue = categoryPermissions.getOrDefault(option.name, false);
                    option.toggleState = currentValue;
                }
            }
        }

        Pokecobbleclaim.LOGGER.debug("Refreshed permission toggles for player " + targetPlayer.getName());
    }

    /**
     * Gets the parent screen.
     *
     * @return The parent screen
     */
    public Screen getParentScreen() {
        return parent;
    }

    /**
     * Plays a click sound.
     */
    private void playClickSound() {
        SoundUtil.playButtonClickSound();
    }

    /**
     * Plays a success sound.
     */
    private void playSuccessSound() {
        this.client.getSoundManager().play(net.minecraft.client.sound.PositionedSoundInstance.master(
            net.minecraft.sound.SoundEvents.ENTITY_PLAYER_LEVELUP, 0.8F));
    }

    /**
     * Gets an icon for a category.
     */
    private String getCategoryIcon(String categoryName) {
        switch (categoryName) {
            case "Rank": return "👑";
            case "Player Management": return "👥";
            case "Claim Tool": return "🔧";
            case "Town Bank": return "💰";
            case "Town Settings": return "⚙"; // Fixed: removed VS16 variant selector
            default: return "📋";
        }
    }



    /**
     * Draws a button with text.
     */
    private void drawButton(DrawContext context, int x, int y, int width, int height, int color, boolean isHovered, String text) {
        // Draw button background
        int baseColor = isHovered ? lightenColor(color, 20) : color;
        context.fill(x, y, x + width, y + height, baseColor);

        // Draw button border
        context.drawBorder(x, y, width, height, 0xFFFFFFFF);

        // Draw button text
        context.drawCenteredTextWithShadow(this.textRenderer, text,
            x + width / 2, y + height / 2 - 4, 0xFFFFFF);
    }

    /**
     * Lightens a color by the specified amount.
     */
    private int lightenColor(int color, int amount) {
        int r = Math.min(255, ((color >> 16) & 0xFF) + amount);
        int g = Math.min(255, ((color >> 8) & 0xFF) + amount);
        int b = Math.min(255, (color & 0xFF) + amount);
        return 0xFF000000 | (r << 16) | (g << 8) | b;
    }



    /**
     * Class representing a permission category with options.
     */
    private static class PermissionCategory {
        private final String name;
        private final String description;
        private final List<PermissionOption> options;
        private int selectedOption; // For rank selection
        private final boolean isToggleCategory; // True for permission toggles, false for rank selection

        // Constructor for rank selection
        public PermissionCategory(String name, String description, List<String> optionNames, int selectedOption) {
            this.name = name;
            this.description = description;
            this.options = new ArrayList<>();
            for (String optionName : optionNames) {
                this.options.add(new PermissionOption(optionName, false)); // Default toggle state
            }
            this.selectedOption = selectedOption;
            this.isToggleCategory = false;
        }

        // Constructor for permission toggles
        public PermissionCategory(String name, String description, List<String> optionNames, Map<String, Boolean> permissionStates) {
            this.name = name;
            this.description = description;
            this.options = new ArrayList<>();
            for (String optionName : optionNames) {
                boolean toggleState = permissionStates.getOrDefault(optionName, false);
                this.options.add(new PermissionOption(optionName, toggleState));
            }
            this.selectedOption = 0; // Not used for toggle categories
            this.isToggleCategory = true;
        }
    }

    /**
     * Class representing a permission option with its toggle button position.
     */
    private static class PermissionOption {
        private final String name;
        private boolean toggleState; // For permission toggles

        public PermissionOption(String name, boolean toggleState) {
            this.name = name;
            this.toggleState = toggleState;
        }

        @Override
        public String toString() {
            return name;
        }
    }
}
