package com.pokecobble.town.gui;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.Pokecobbleclaim;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.screen.Screen;

import java.util.UUID;

/**
 * Utility class for checking permissions and showing appropriate permission denied screens.
 */
public class PermissionChecker {

    /**
     * Checks if a player has a specific permission and shows a permission denied screen if not.
     *
     * @param parentScreen The screen to return to
     * @param town The player's town
     * @param playerId The player's UUID
     * @param permissionCategory The permission category (e.g., "Player Management")
     * @param permissionName The specific permission (e.g., "Can invite players")
     * @param featureName The user-friendly name of the feature (e.g., "player invitations")
     * @return true if the player has permission, false if permission denied screen was shown
     */
    public static boolean checkPermissionOrShowDenied(Screen parentScreen, Town town, UUID playerId,
                                                     String permissionCategory, String permissionName,
                                                     String featureName) {
        if (hasPermission(town, playerId, permissionCategory, permissionName)) {
            return true;
        }

        // Show permission denied screen
        MinecraftClient client = MinecraftClient.getInstance();
        client.setScreen(new PermissionDeniedScreen(parentScreen, town, featureName, permissionCategory, permissionName));
        return false;
    }

    /**
     * Checks if a player has a specific permission and shows a notification if not.
     * This method is specifically designed for MyTownScreen to show notifications instead of opening screens.
     *
     * @param myTownScreen The MyTownScreen instance to show notifications on
     * @param town The player's town
     * @param playerId The player's UUID
     * @param permissionCategory The permission category (e.g., "Player Management")
     * @param permissionName The specific permission (e.g., "Can invite players")
     * @param featureName The user-friendly name of the feature (e.g., "player invitations")
     * @return true if the player has permission, false if permission denied notification was shown
     */
    public static boolean checkPermissionOrShowNotification(MyTownScreen myTownScreen, Town town, UUID playerId,
                                                           String permissionCategory, String permissionName,
                                                           String featureName) {
        if (hasPermission(town, playerId, permissionCategory, permissionName)) {
            return true;
        }

        // Show permission denied notification
        myTownScreen.showPermissionNotification(featureName);
        return false;
    }

    /**
     * Checks if a player has a specific permission.
     *
     * @param town The player's town
     * @param playerId The player's UUID
     * @param permissionCategory The permission category
     * @param permissionName The specific permission
     * @return true if the player has permission, false otherwise
     */
    public static boolean hasPermission(Town town, UUID playerId, String permissionCategory, String permissionName) {
        if (town == null || playerId == null) {
            return false;
        }

        TownPlayer townPlayer = town.getPlayer(playerId);
        if (townPlayer == null) {
            // Player data might not be synchronized yet after rejoining server
            // Request fresh player data and return false for now
            requestPlayerDataSync(playerId);
            return false;
        }

        // Owners and admin viewers have all permissions
        if (townPlayer.getRank().hasAdminPermissions()) {
            return true;
        }

        // Check specific permission
        return townPlayer.hasPermission(permissionCategory, permissionName);
    }

    /**
     * Requests player data synchronization from the server.
     * This is called when permission checks fail due to missing player data.
     * Note: With the new simplified permission system, permissions are automatically synced on connection.
     */
    private static void requestPlayerDataSync(UUID playerId) {
        // With the new simplified permission system, permissions are automatically synced on connection
        // No need to manually request sync - just log for debugging
        Pokecobbleclaim.LOGGER.debug("Permission check failed due to missing player data - permissions should auto-sync on connection");
    }

    /**
     * Permission definitions for different features
     */
    public static class Permissions {
        // Player Management
        public static final String PLAYER_MANAGEMENT = "Player Management";
        public static final String CAN_INVITE_PLAYERS = "Can invite players";
        public static final String CAN_KICK_PLAYERS = "Can kick players";
        public static final String CAN_CHANGE_PLAYER_RANKS = "Can change player ranks";
        public static final String CAN_MANAGE_PLAYER_PERMISSIONS = "Can manage player permissions";

        // Job
        public static final String JOB = "Job";
        public static final String CAN_REQUEST_BUILDING_APPROVAL = "Can request building approval";
        public static final String CAN_VIEW_JOB_APPLICATIONS = "Can view job applications";
        public static final String CAN_APPLY_FOR_JOBS = "Can apply for jobs";
        public static final String CAN_MANAGE_TOWN_JOBS = "Can manage town jobs";

        // Town Settings
        public static final String TOWN_SETTINGS = "Town Settings";
        public static final String CAN_MODIFY_SETTINGS = "Can modify settings";
        public static final String CAN_CHANGE_TOWN_NAME = "Can change town name";
        public static final String CAN_CHANGE_TOWN_DESCRIPTION = "Can change town description";
        public static final String CAN_CHANGE_TOWN_SPAWN = "Can change town spawn";

        // Town Bank
        public static final String TOWN_BANK = "Town Bank";
        public static final String CAN_VIEW_BALANCE = "Can view balance";
        public static final String CAN_DEPOSIT_MONEY = "Can deposit money";
        public static final String CAN_WITHDRAW_MONEY = "Can withdraw money";
        public static final String CAN_VIEW_TRANSACTIONS = "Can view transactions";

        // Town Level
        public static final String TOWN_LEVEL = "Town Level";
        public static final String CAN_VIEW_LEVEL = "Can view level";
        public static final String CAN_CONTRIBUTE_RESOURCES = "Can contribute resources";
        public static final String CAN_VIEW_BENEFITS = "Can view benefits";

        // Claim Tool
        public static final String CLAIM_TOOL = "Claim Tool";
        public static final String CAN_VIEW_CLAIMS = "Can view claims";
        public static final String CAN_ACCESS_CLAIM_TOOL = "Can access claim tool";
        public static final String CAN_DELETE_CLAIMS = "Can delete claims";
    }

    /**
     * Feature names for user-friendly error messages
     */
    public static class FeatureNames {
        public static final String PLAYER_INVITATIONS = "player invitations";
        public static final String PLAYER_MANAGEMENT = "player management";
        public static final String JOB_SYSTEM = "job system";
        public static final String TOWN_SETTINGS = "town settings";
        public static final String TOWN_BANK = "town bank";
        public static final String TOWN_LEVEL = "town level system";
        public static final String CLAIM_TOOL = "claim tool";

        public static final String PLAYER_INFO = "player information";
        public static final String VOTING = "voting system";
        public static final String TOWN_BENEFITS = "town benefits";
        public static final String TOWN_CONTRIBUTIONS = "town contributions";
    }
}
